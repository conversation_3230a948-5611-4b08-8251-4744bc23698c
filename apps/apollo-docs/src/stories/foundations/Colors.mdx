import { Meta } from "@storybook/addon-docs/blocks"
import { ColorPalette } from "./color-palette"

<Meta title="Foundations/Colors" tags={["docs"]} />

# Colors

The Apollo design system provides a comprehensive color palette that supports both light and dark themes. All colors are defined as CSS custom properties (variables) and are organized into logical groups for easy use and maintenance.

The color palette is organized into two main collections of variables in Figma [💙 Apollo Alias Foundations and Styles](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=13-3&t=UZXeBdRDW7D0SWTy-1):

- **Alias**: Semantic color mappings organized by usage roles (Primary, On Primary, Secondary, etc.), use reference color from `Base`.
- **Base**: Foundational color primitives organized by color families (Primary, Secondary, Neutral, etc.)

Use the search functionality to quickly find specific colors across both sections. The search will filter colors in real-time across all categories.

<ColorPalette />

## Usage

### Recommended: Use Alias Colors

For most use cases, use alias colors as they provide semantic meaning and automatic theme adaptation:

```css
.primary-button {
  background-color: var(--apl-alias-color-primary-primary);
  color: var(--apl-alias-color-primary-on-primary);
}

.error-message {
  background-color: var(--apl-alias-color-error-error-container);
  color: var(--apl-alias-color-error-error);
}

.card {
  background-color: var(--apl-alias-color-background-and-surface-surface);
  color: var(--apl-alias-color-background-and-surface-on-surface);
}
```

### Color Naming Convention

Our color tokens follow a consistent naming pattern:

- **Base colors**: `--apl-base-color-{category}`
  - Example: `--apl-base-color-primary-40`
- **Alias colors**: `--apl-alias-color-{category}-{role}`
  - Example: `--apl-alias-color-primary-primary`


## Light and Dark Theme Support

### Automatic Theme Adaptation with `light-dark()`

**All alias colors automatically adapt to light and dark themes using the CSS `light-dark()` function.** This modern CSS feature allows colors to automatically switch between two values based on the user's system preference or manually set theme.

**How it works:**
- **Light theme**: Uses the first value in `light-dark(lightValue, darkValue)`
- **Dark theme**: Uses the second value in `light-dark(lightValue, darkValue)`
- **Automatic detection**: Responds to `prefers-color-scheme` media query
- **Manual override**: Can be controlled via `color-scheme` CSS property