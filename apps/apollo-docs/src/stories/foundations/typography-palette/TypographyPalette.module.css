.typographyControls {
  margin-bottom: 2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.typographySections {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.typographySection {
  width: 100%;
}

.sectionHeading {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--apl-alias-color-outline-and-border-border);
}

.typographySearch {
  width: 100%;
}

.typographyGroup {
  margin-bottom: 3rem;
}

.typographySubgroup {
  margin-bottom: 2rem;
}

.typographySubgroupTitle {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--sb-primary-text-color);
  position: relative;
  margin-bottom: 1rem;
}

/* Typography Table Styles */
.typographyTableContainer {
  margin-top: 1.5rem;
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid var(--apl-alias-color-outline-and-border-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.typographyTable {
  width: 100%;
  border-collapse: collapse;
  background: var(--apl-alias-color-background-and-surface-surface);
  font-size: 0.875rem;
}

.typographyTableHeader {
  background: var(--apl-alias-color-background-and-surface-surface-variant);
  border-bottom: 2px solid var(--apl-alias-color-outline-and-border-border);
}

.headerCell {
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-right: 1px solid var(--apl-alias-color-outline-and-border-border);
}

.headerCell:last-child {
  border-right: none;
}

.typographyTableRow {
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-border);
  transition: background-color 0.2s ease;
}

.typographyTableRow:nth-child(even) {
  background: var(--apl-alias-color-background-and-surface-surface-variant);
}

.typographyTableRow:hover {
  background: var(--apl-alias-color-background-and-surface-surface-variant);
}

.typographyTableRow:last-child {
  border-bottom: none;
}

.styleNameCell {
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  color: var(--apl-alias-color-primary-primary);
  border-right: 1px solid var(--apl-alias-color-outline-and-border-border);
  white-space: nowrap;
}

.previewCell {
  padding: 1.25rem 1rem;
  border-right: 1px solid var(--apl-alias-color-outline-and-border-border);
  min-width: 140px;
  max-width: 250px;
}

.previewText {
  color: var(--apl-alias-color-background-and-surface-on-surface);
  line-height: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.propertiesCell {
  padding: 1.25rem 1rem;
  vertical-align: top;
  min-width: 200px;
}

.propertyList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.propertyItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.propertyLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.propertyValue {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-weight: 500;
}

.tokensCell {
  padding: 1.25rem 1.5rem;
  vertical-align: top;
  min-width: 150px;
  border-right: 1px solid var(--apl-alias-color-outline-and-border-border);
}

.tokenList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tokenItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tokenLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tokenValue {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  word-break: break-all;
}

@media (max-width: 768px) {
  .typographyTableContainer {
    margin-top: 1rem;
  }

  .headerCell,
  .styleNameCell,
  .previewCell,
  .propertiesCell,
  .tokensCell {
    padding: 1rem;
  }

  .typographyTable {
    font-size: 0.75rem;
  }

  .previewCell {
    min-width: 120px;
    max-width: 140px;
  }

  .propertiesCell {
    min-width: 160px;
    max-width: 180px;
  }

  .tokensCell {
    min-width: 250px;
  }

  .propertyValue {
    font-size: 0.75rem;
  }

  .tokenLabel {
    font-size: 0.625rem;
  }

  .tokenValue {
    font-size: 0.625rem;
  }

  .tokenItem {
    padding: 0.375rem;
  }
}

@media (max-width: 480px) {
  .typographyTableContainer {
    border-radius: 8px;
  }

  .headerCell,
  .styleNameCell,
  .previewCell,
  .propertiesCell,
  .tokensCell {
    padding: 0.75rem 0.5rem;
  }

  .typographyTable {
    font-size: 0.625rem;
  }

  .headerCell {
    font-size: 0.625rem;
  }

  .previewCell {
    min-width: 100px;
    max-width: 120px;
  }

  .propertiesCell {
    min-width: 140px;
    max-width: 160px;
  }

  .tokensCell {
    min-width: 200px;
  }

  .previewText {
    font-size: 0.75em;
  }

  .propertyLabel {
    font-size: 0.625rem;
  }

  .propertyValue {
    font-size: 0.625rem;
  }

  .tokenLabel {
    font-size: 0.5rem;
  }

  .tokenValue {
    font-size: 0.5rem;
  }

  .tokenItem {
    padding: 0.25rem;
  }
}

/* Base Typography Tokens Styles */
.baseTokensTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
}

.baseTokensList {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.baseTokensCategory {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.baseTokensCategoryTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  margin: 0;
  padding-bottom: 0.5rem;
}

.baseTokensItems {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.baseTokensItem {
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-border);
}

.baseTokensItem:last-child {
  border-bottom: none;
}

.baseTokensItemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.baseTokensValue {
  flex-shrink: 0;
}

.baseTokensCode {
  font-size: 1rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  word-break: break-all;
}

/* Responsive styles for base tokens */
@media (max-width: 768px) {
  .baseTokensTitle {
    font-size: 1.25rem;
    margin: 1.5rem 0 0.75rem 0;
  }

  .baseTokensList {
    gap: 1.5rem;
  }

  .baseTokensCategoryTitle {
    font-size: 1.125rem;
  }

  .baseTokensItems {
    gap: 0.5rem;
  }

  .baseTokensItem {
    padding: 0.5rem 0;
  }

  .baseTokensItemHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .baseTokensCode {
    font-size: 0.625rem;
    padding: 0.25rem 0.375rem;
  }
}

@media (max-width: 480px) {
  .baseTokensTitle {
    font-size: 1.125rem;
    margin: 1rem 0 0.5rem 0;
  }

  .baseTokensList {
    gap: 1rem;
  }

  .baseTokensCategoryTitle {
    font-size: 1rem;
  }

  .baseTokensItems {
    gap: 0.375rem;
  }

  .baseTokensItem {
    padding: 0.375rem 0;
  }

  .baseTokensItemHeader {
    gap: 0.375rem;
  }

  .baseTokensCode {
    font-size: 0.5rem;
    padding: 0.25rem;
  }
}

.typographySwatch {
  display: flex;
  flex-direction: column;
  background: none;
  border: 1px solid var(--apl-alias-color-outline-and-border-border);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  text-align: left;
}

.typographySwatch:hover {
  border-color: var(--apl-alias-color-primary-primary);
  box-shadow: 0 4px 12px var(--apl-alias-color-effects-shadow);
  transform: translateY(-2px);
}

.typographySwatchPreview {
  width: 100%;
  min-height: 120px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--apl-alias-color-outline-and-border-border);
  padding: 1.5rem;
  background-color: var(--apl-alias-color-background-and-surface-surface);
}

.typographySwatchText {
  color: var(--apl-alias-color-background-and-surface-on-surface);
  text-align: center;
  word-break: break-word;
}

.typographySwatchInfo {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.typographySwatchVariable {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  color: var(--apl-alias-color-primary-primary);
  font-weight: 500;
  word-break: break-all;
  line-height: 1.4;
}

.typographySwatchValue {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.75rem;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  font-weight: 600;
}

.typographySwatchProperties {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.typographySwatchProperty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.typographySwatchPropertyLabel {
  color: var(--apl-alias-color-background-and-surface-on-surface-variant);
  font-weight: 500;
}

.typographySwatchPropertyValue {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: var(--apl-alias-color-background-and-surface-on-surface);
  font-weight: 600;
}

.copiedIndicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--apl-alias-color-success-success-container);
  color: var(--apl-alias-color-success-success);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px var(--apl-alias-color-effects-shadow);
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.tabContent {
  min-height: 400px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
